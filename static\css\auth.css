/* 认证页面专用样式 */

/* ==================== 基础样式 ==================== */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ==================== 动态背景 ==================== */
.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(1px);
    animation: floating 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    background: rgba(106, 90, 205, 0.15);
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.08);
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    background: rgba(138, 43, 226, 0.12);
    top: 40%;
    right: 40%;
    animation-delay: 1s;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

/* ==================== 主要内容区域 ==================== */
.auth-content {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 440px;
    padding: 20px;
}

.register-content {
    max-width: 600px;
}

/* ==================== 认证卡片 ==================== */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    padding: 40px 35px;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 10px 20px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
}

.register-card {
    padding: 35px 40px;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

/* ==================== 返回链接 ==================== */
.back-link {
    display: inline-flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.6);
    text-decoration: none;
    font-size: 14px;
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.back-link:hover {
    color: #667eea;
    transform: translateX(-5px);
}

.back-link i {
    margin-right: 8px;
    font-size: 16px;
}

/* ==================== 认证头部 ==================== */
.auth-header {
    text-align: center;
    margin-bottom: 35px;
}

.auth-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-subtitle {
    color: #718096;
    font-size: 16px;
    line-height: 1.5;
}

/* 已删除location-hint相关样式 */

/* ==================== 表单样式 ==================== */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.register-form {
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 640px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* ==================== 输入组件 ==================== */
.input-group {
    position: relative;
    margin-bottom: 20px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.input-wrapper input {
    width: 100%;
    height: 56px;
    padding: 20px 45px 12px 45px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    color: #2d3748;
    position: relative;
    z-index: 1;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    /* 防止自动填充样式干扰 */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.input-wrapper input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
}

/* 自动填充样式覆盖 */
.input-wrapper input:-webkit-autofill,
.input-wrapper input:-webkit-autofill:hover,
.input-wrapper input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.9) inset !important;
    -webkit-text-fill-color: #2d3748 !important;
    border-color: #667eea !important;
    transition: background-color 5000s ease-in-out 0s;
}

.input-wrapper input:-webkit-autofill + label {
    transform: translateY(-40px) scale(0.85);
    color: #667eea;
    background: rgba(255, 255, 255, 0.95);
    padding: 2px 8px;
    border-radius: 4px;
    z-index: 3;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-wrapper input:focus + label,
.input-wrapper.focused label {
    transform: translateY(-40px) scale(0.85);
    color: #667eea;
    background: rgba(255, 255, 255, 0.95);
    padding: 2px 8px;
    border-radius: 4px;
    z-index: 3;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-wrapper label {
    position: absolute;
    top: 50%;
    left: 45px;
    transform: translateY(-50%);
    color: #718096;
    font-size: 16px;
    font-weight: 500;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: transparent;
    z-index: 2;
    white-space: nowrap;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 16px;
    z-index: 2;
    transition: color 0.3s ease;
    pointer-events: none;
}

.input-wrapper:focus-within .input-icon {
    color: #667eea;
}

/* ==================== 密码相关组件 ==================== */
.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    font-size: 16px;
    z-index: 3;
    transition: color 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #667eea;
}

.password-strength {
    margin-top: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.password-strength.show {
    opacity: 1;
}

.strength-bar {
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-bar::after {
    content: '';
    display: block;
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.strength-bar.weak::after { width: 33%; background: #ef4444; }
.strength-bar.medium::after { width: 66%; background: #f59e0b; }
.strength-bar.strong::after { width: 100%; background: #10b981; }

.strength-text {
    font-size: 12px;
    color: #718096;
}

.password-match {
    margin-top: 8px;
    font-size: 12px;
    color: #10b981;
    display: flex;
    align-items: center;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.password-match.show {
    opacity: 1;
}

.password-match.error {
    color: #ef4444;
}

.password-match.error i::before {
    content: "\f00d";
}

/* ==================== 学校选择区域 ==================== */
.school-selection {
    background: rgba(247, 250, 252, 0.6);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    margin-bottom: 16px;
}

.selection-title {
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.selection-title i {
    color: #667eea;
}

/* 已删除location-suggestion相关样式 */

/* ==================== 筛选组件 ==================== */
.search-filters {
    margin-bottom: 20px;
}

.filter-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.filter-select {
    height: 44px;
    padding: 10px 35px 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    border-color: #667eea;
    outline: none;
}

/* ==================== 学校选择 ==================== */
.school-select-wrapper {
    position: relative;
    margin-bottom: 8px;
}

.school-select {
    width: 100%;
    height: 48px;
    padding: 12px 40px 12px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.95);
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.school-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.school-select:focus + .select-arrow {
    color: #667eea;
}

.select-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    pointer-events: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

/* ==================== 学校信息卡片 ==================== */
.school-info-card {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    padding: 10px 12px;
    opacity: 0;
    transform: translateY(-8px);
    transition: all 0.3s ease;
    pointer-events: none;
    margin-top: 4px;
}

.school-info-card.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.info-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 3px;
    line-height: 1.3;
}

.info-content p {
    font-size: 13px;
    color: #718096;
    margin-bottom: 3px;
    line-height: 1.3;
}

.info-content span {
    font-size: 12px;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 1px 6px;
    border-radius: 12px;
}

/* ==================== 表单选项 ==================== */
.form-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #4a5568;
}

.checkbox-wrapper input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-wrapper input:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-wrapper input:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 4px;
    height: 8px;
    border: 2px solid white;
    border-left: none;
    border-top: none;
    transform: translate(-50%, -60%) rotate(45deg);
}

/* ==================== 按钮样式 ==================== */
.auth-btn {
    height: 52px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    /* 提高可访问性 */
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.auth-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.auth-btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.auth-btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.auth-btn-primary:active {
    transform: translateY(0);
}

.register-btn {
    margin-top: 10px;
}

/* 按钮加载状态 */
.auth-btn.loading .btn-text {
    opacity: 0;
}

.auth-btn.loading .btn-loading {
    opacity: 1;
}

.btn-loading {
    position: absolute;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 分割线 ==================== */
.auth-divider {
    margin: 30px 0;
    position: relative;
    text-align: center;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.auth-divider span {
    background: rgba(255, 255, 255, 0.9);
    color: #718096;
    padding: 0 20px;
    font-size: 14px;
}

/* ==================== 页脚链接 ==================== */
.auth-footer {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
}

.auth-footer p {
    color: #718096;
    margin: 0;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.auth-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

/* ==================== 错误消息 ==================== */
.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .auth-content {
        padding: 15px;
    }
    
    .auth-card {
        padding: 30px 25px;
        margin: 10px;
    }
    
    .register-card {
        padding: 25px 20px;
    }
    
    .auth-title {
        font-size: 24px;
    }
    
    .school-selection {
        padding: 14px;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    /* 移动端优化 */
    .input-wrapper input {
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .auth-btn {
        min-height: 48px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .auth-card {
        border-radius: 16px;
        padding: 25px 20px;
    }
    
    .auth-title {
        font-size: 22px;
    }
    
    .input-wrapper input {
        height: 48px;
        font-size: 15px;
    }
    
    .auth-btn {
        height: 48px;
        font-size: 15px;
    }
}

/* ==================== 动画和过渡效果 ==================== */
.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-up {
    animation: slideUp 0.4s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==================== 特殊效果 ==================== */
.auth-card::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2, #667eea);
    border-radius: 26px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-card:hover::after {
    opacity: 0.1;
}