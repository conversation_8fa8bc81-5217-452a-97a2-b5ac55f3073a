from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import <PERSON>ginManager, UserMixin, login_user, login_required, logout_user, current_user
from config import config
from models import Database, School, User as UserModel, Post, Comment
import os
import requests
import json

app = Flask(__name__)
app.config.from_object(config['development'])

# 初始化数据库
db = Database()
school_model = School(db)
user_model = UserModel(db)
post_model = Post(db)
comment_model = Comment(db)

# 初始化Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录后访问该页面。'

# 用户类
class User(UserMixin):
    def __init__(self, id, username, email, school_id, is_admin=False):
        self.id = id
        self.username = username
        self.email = email
        self.school_id = school_id
        self.is_admin = is_admin

@login_manager.user_loader
def load_user(user_id):
    user = user_model.get_by_id(user_id)
    if user:
        return User(user['id'], user['username'], user['email'], user['school_id'], user['is_admin'])
    return None

def get_user_location_by_ip(ip_address):
    """根据IP地址获取用户地理位置信息"""
    try:
        # 使用免费的IP地理位置API
        if ip_address == '127.0.0.1' or ip_address.startswith('192.168') or ip_address.startswith('10.'):
            # 本地IP地址，返回默认位置（北京）
            return {'province': '北京', 'city': '北京市'}
        
        # 使用ip-api.com免费服务
        response = requests.get(f'http://ip-api.com/json/{ip_address}?lang=zh-CN', timeout=3)
        if response.status_code == 200:
            data = response.json()
            if data['status'] == 'success':
                province = data.get('regionName', '')
                city = data.get('city', '')
                
                # 处理省份名称格式
                if province and not province.endswith('省') and not province.endswith('市'):
                    if province in ['北京', '上海', '天津', '重庆']:
                        province = province
                    else:
                        province = province + '省'
                
                return {'province': province, 'city': city}
    except Exception as e:
        print(f"IP地理位置检测失败: {e}")
    
    # 检测失败时返回None
    return None

def get_schools_by_location(province, city):
    """根据地理位置获取学校列表"""
    # 优先显示城市的学校
    if city:
        schools = school_model.get_by_city(city)
        if schools:
            return schools
    
    # 如果城市没有学校，显示省份的学校
    if province:
        schools = school_model.get_by_province(province)
        if schools:
            return schools[:8]  # 限制显示数量
    
    # 如果省份也没有学校，返回热门学校
    return school_model.get_all()[:8]

# 路由定义
@app.route('/')
def index():
    """首页 - 根据用户IP显示本地学校和热门帖子"""
    # 获取用户IP地址
    user_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', '127.0.0.1'))
    if ',' in user_ip:
        user_ip = user_ip.split(',')[0].strip()
    
    # 获取用户地理位置
    location = get_user_location_by_ip(user_ip)
    
    # 根据地理位置获取学校
    if location:
        schools = get_schools_by_location(location['province'], location['city'])
        location_info = f"{location['province']}{location['city']}"
    else:
        # 检测失败时显示热门学校
        schools = school_model.get_all()[:8]
        location_info = "全国"
    
    hot_posts = post_model.get_hot_posts(limit=5)
    
    return render_template('index.html', 
                         schools=schools, 
                         hot_posts=hot_posts,
                         location_info=location_info,
                         user_location=location)

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        school_id = request.form['school_id']
        
        # 验证输入
        if not all([username, email, password, confirm_password, school_id]):
            flash('请填写所有字段', 'error')
            return redirect(url_for('register'))
        
        if password != confirm_password:
            flash('两次输入的密码不一致', 'error')
            return redirect(url_for('register'))
        
        if len(password) < 6:
            flash('密码长度至少6位', 'error')
            return redirect(url_for('register'))
        
        # 检查用户名和邮箱是否已存在
        if user_model.get_by_username(username):
            flash('用户名已存在', 'error')
            return redirect(url_for('register'))
        
        if user_model.get_by_email(email):
            flash('邮箱已被注册', 'error')
            return redirect(url_for('register'))
        
        # 创建用户
        user_id = user_model.create(username, email, password, school_id)
        if user_id:
            flash('注册成功，请登录', 'success')
            return redirect(url_for('login'))
        else:
            flash('注册失败，请重试', 'error')
    
    schools = school_model.get_all()
    return render_template('register.html', schools=schools)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'POST':
        username_or_email = request.form['username']
        password = request.form['password']
        
        if not all([username_or_email, password]):
            flash('请填写用户名和密码', 'error')
            return redirect(url_for('login'))
        
        # 尝试通过用户名或邮箱查找用户
        user = user_model.get_by_username(username_or_email)
        if not user:
            user = user_model.get_by_email(username_or_email)
        
        if user and user_model.verify_password(user, password):
            user_obj = User(user['id'], user['username'], user['email'], user['school_id'], user['is_admin'])
            login_user(user_obj)
            flash('登录成功', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('用户名或密码错误', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('已成功登出', 'success')
    return redirect(url_for('index'))

@app.route('/school/<int:school_id>')
def school_board(school_id):
    """学校板块页面"""
    school = school_model.get_by_id(school_id)
    if not school:
        flash('学校不存在', 'error')
        return redirect(url_for('index'))
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page
    
    posts = post_model.get_by_school(school_id, limit=per_page, offset=offset)
    return render_template('school_board.html', school=school, posts=posts, page=page)

@app.route('/post/<int:post_id>')
def post_detail(post_id):
    """帖子详情页面"""
    post = post_model.get_by_id(post_id)
    if not post:
        flash('帖子不存在', 'error')
        return redirect(url_for('index'))
    
    # 增加浏览量
    post_model.increment_view_count(post_id)
    
    # 获取评论
    comments = comment_model.get_by_post(post_id)
    
    return render_template('post_detail.html', post=post, comments=comments)

@app.route('/create_post', methods=['GET', 'POST'])
@login_required
def create_post():
    """创建帖子"""
    if request.method == 'POST':
        title = request.form['title']
        content = request.form['content']
        
        if not all([title, content]):
            flash('请填写标题和内容', 'error')
            return redirect(url_for('create_post'))
        
        # 创建帖子
        post_id = post_model.create(title, content, current_user.id, current_user.school_id)
        if post_id:
            flash('发帖成功', 'success')
            return redirect(url_for('post_detail', post_id=post_id))
        else:
            flash('发帖失败，请重试', 'error')
    
    return render_template('create_post.html')

@app.route('/api/comment', methods=['POST'])
@login_required
def add_comment():
    """添加评论API"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': '无效的请求数据'})
    
    content = data.get('content')
    post_id = data.get('post_id')
    parent_id = data.get('parent_id')
    
    if not all([content, post_id]):
        return jsonify({'success': False, 'message': '请填写评论内容'})
    
    # 验证帖子是否存在
    post = post_model.get_by_id(post_id)
    if not post:
        return jsonify({'success': False, 'message': '帖子不存在'})
    
    # 创建评论
    comment_id = comment_model.create(content, post_id, current_user.id, parent_id)
    if comment_id:
        return jsonify({'success': True, 'message': '评论成功', 'comment_id': comment_id})
    else:
        return jsonify({'success': False, 'message': '评论失败，请重试'})

@app.route('/search/<int:school_id>')
def search_posts(school_id):
    """搜索帖子"""
    keyword = request.args.get('q', '')
    if not keyword:
        return redirect(url_for('school_board', school_id=school_id))
    
    school = school_model.get_by_id(school_id)
    if not school:
        flash('学校不存在', 'error')
        return redirect(url_for('index'))
    
    posts = post_model.search_in_school(school_id, keyword)
    return render_template('search_results.html', school=school, posts=posts, keyword=keyword)

@app.route('/profile')
@login_required
def profile():
    """个人中心"""
    user_info = user_model.get_user_with_school(current_user.id)
    user_posts = post_model.get_by_school(current_user.school_id, limit=50)  # 获取用户的帖子
    # 过滤出当前用户的帖子
    user_posts = [post for post in user_posts if post['user_id'] == current_user.id]
    return render_template('profile.html', user_info=user_info, user_posts=user_posts)

@app.route('/schools')
def schools_list():
    """学校列表页面，支持筛选"""
    province = request.args.get('province')
    city = request.args.get('city')
    school_type = request.args.get('type')
    keyword = request.args.get('q')
    
    if keyword:
        schools = school_model.search_schools(keyword, province, city, school_type)
    elif province or city or school_type:
        if province and not city and not school_type:
            schools = school_model.get_by_province(province)
        elif city:
            schools = school_model.get_by_city(city)
        elif school_type:
            schools = school_model.get_by_type(school_type)
        else:
            schools = school_model.search_schools('', province, city, school_type)
    else:
        schools = school_model.get_all()
    
    # 获取筛选选项
    provinces = school_model.get_provinces()
    school_types = school_model.get_school_types()
    cities = school_model.get_cities_by_province(province) if province else []
    
    return render_template('schools_list.html', 
                         schools=schools, 
                         provinces=provinces, 
                         cities=cities,
                         school_types=school_types,
                         selected_province=province,
                         selected_city=city,
                         selected_type=school_type,
                         keyword=keyword)

@app.route('/api/cities/<province>')
def get_cities(province):
    """根据省份获取城市列表API"""
    cities = school_model.get_cities_by_province(province)
    return jsonify({'cities': cities})

@app.route('/api/schools/search')
def search_schools_api():
    """学校搜索API"""
    keyword = request.args.get('q', '')
    province = request.args.get('province')
    city = request.args.get('city')
    school_type = request.args.get('type')
    limit = request.args.get('limit', 20, type=int)
    
    schools = school_model.search_schools(keyword, province, city, school_type, limit)
    
    # 转换为JSON格式
    schools_data = []
    for school in schools:
        schools_data.append({
            'id': school['id'],
            'name': school['name'],
            'province': school['province'],
            'city': school['city'],
            'school_type': school['school_type'],
            'level': school['level']
        })
    
    return jsonify({'schools': schools_data})

@app.route('/api/provinces')
def get_provinces():
    """获取所有省份列表API"""
    provinces = school_model.get_provinces()
    return jsonify(provinces)

@app.route('/api/school_types')
def get_school_types():
    """获取所有学校类型列表API"""
    school_types = school_model.get_school_types()
    return jsonify(school_types)

@app.route('/api/cities/<province>')
def get_cities_api(province):
    """根据省份获取城市列表API"""
    cities = school_model.get_cities_by_province(province)
    return jsonify(cities)

@app.route('/api/get_user_location')
def get_user_location_api():
    """获取用户IP定位信息API"""
    try:
        # 获取用户IP地址
        user_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', '127.0.0.1'))
        if ',' in user_ip:
            user_ip = user_ip.split(',')[0].strip()

        # 获取地理位置信息
        location = get_user_location_by_ip(user_ip)

        if location:
            return jsonify({
                'success': True,
                'province': location.get('province', ''),
                'city': location.get('city', ''),
                'ip': user_ip
            })
        else:
            return jsonify({
                'success': False,
                'message': '无法获取位置信息',
                'ip': user_ip
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'定位服务错误: {str(e)}',
            'ip': '未知'
        })

if __name__ == '__main__':
    db.init_database()
    app.run(debug=False, use_reloader=False, host='127.0.0.1', port=5555)