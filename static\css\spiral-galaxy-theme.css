/* 螺旋星系主题样式 - 基于新设计 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    line-height: 1.6;
    color: #e1e6fa;
    background-color: #000;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 导航栏样式 */
.navbar {
    background: rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: white;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar:hover {
    background: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 70px;
}

.nav-brand a {
    color: white;
    text-decoration: none;
    font-size: 22px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(45deg, #06b6d4, #9333ea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3));
}

.nav-brand a:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 2px 8px rgba(6, 182, 212, 0.4));
}

.nav-brand i {
    color: #06b6d4;
    font-size: 20px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-user, .nav-auth {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-text {
    color: #d1d5db;
    font-size: 14px;
    font-weight: 500;
}

.nav-link {
    color: #f8fafc;
    text-decoration: none;
    padding: 10px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    border: 1px solid transparent;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-link:hover {
    background: rgba(6, 182, 212, 0.2);
    border-color: rgba(6, 182, 212, 0.5);
    color: #67e8f9;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
    text-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

.register-btn {
    background: linear-gradient(45deg, #9333ea, #ec4899);
    color: white !important;
    border: none;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.register-btn:hover {
    background: linear-gradient(45deg, #7c3aed, #db2777);
    box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
    color: white !important;
}

/* 消息提示样式 */
.flash-messages {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.flash-message {
    padding: 16px 20px;
    margin-bottom: 12px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease-out;
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.flash-success {
    background: rgba(34, 197, 94, 0.9);
    color: white;
    border-left: 4px solid #22c55e;
}

.flash-error {
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border-left: 4px solid #ef4444;
}

.flash-close {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    font-size: 16px;
    padding: 0;
    margin-left: 12px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.flash-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 主要内容区域 */
.main-content {
    position: relative;
    z-index: 1;
}

/* 页脚样式 */
.footer {
    background: rgba(17, 24, 39, 0.9);
    backdrop-filter: blur(16px);
    border-top: 1px solid rgba(75, 85, 99, 0.3);
    color: #d1d5db;
    text-align: center;
    padding: 30px 0;
    margin-top: 60px;
    position: relative;
    z-index: 1;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 螺旋星系背景容器 */
.spiral-galaxy-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

/* 主螺旋星系 */
.main-spiral {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 800px;
    animation: spiralRotate 60s linear infinite;
}

/* 螺旋臂环 */
.spiral-rings {
    position: absolute;
    inset: 0;
    opacity: 0.8;
}

.spiral-ring {
    position: absolute;
    border-radius: 50%;
    border: 3px solid;
    animation: ringPulse 4s ease-in-out infinite;
}

.spiral-ring:nth-child(1) {
    width: 100%;
    height: 100%;
    border-color: rgba(59, 130, 246, 0.3);
    animation-delay: 0s;
}

.spiral-ring:nth-child(2) {
    width: 83.33%;
    height: 83.33%;
    top: 8.33%;
    left: 8.33%;
    border-color: rgba(147, 51, 234, 0.4);
    animation-delay: 1s;
}

.spiral-ring:nth-child(3) {
    width: 66.67%;
    height: 66.67%;
    top: 16.67%;
    left: 16.67%;
    border-color: rgba(6, 182, 212, 0.5);
    animation-delay: 2s;
}

.spiral-ring:nth-child(4) {
    width: 50%;
    height: 50%;
    top: 25%;
    left: 25%;
    border-color: rgba(236, 72, 153, 0.6);
    animation-delay: 3s;
}

/* 螺旋粒子点 */
.spiral-particles {
    position: absolute;
    inset: 0;
}

.spiral-particle {
    position: absolute;
    width: 1px;
    height: 1px;
    background: white;
    border-radius: 50%;
    animation: particlePulse 3s ease-in-out infinite;
}

/* 第二层螺旋星系 */
.secondary-spiral {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1200px;
    height: 1200px;
    animation: spiralRotate 90s linear infinite reverse;
}

.secondary-particle {
    position: absolute;
    width: 0.5px;
    height: 0.5px;
    background: linear-gradient(45deg, #3b82f6, #9333ea);
    border-radius: 50%;
    animation: particlePulse 4s ease-in-out infinite;
    box-shadow: 0 0 2px #4f46e5;
}

/* 浮动星尘粒子 */
.floating-stardust {
    position: absolute;
    width: 1px;
    height: 1px;
    background: white;
    border-radius: 50%;
    animation: stardustFloat 15s linear infinite;
    opacity: 0.3;
}

/* 流星效果 */
.meteor {
    position: absolute;
    width: 1px;
    height: 1px;
    background: linear-gradient(45deg, white, transparent);
    border-radius: 50%;
    transform: rotate(-45deg);
    box-shadow: 0 0 20px 2px rgba(255,255,255,0.8), 0 0 40px 4px rgba(100,200,255,0.3);
    animation: meteorFly 4s linear infinite;
}

/* 径向渐变覆盖层 */
.radial-overlay {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 50% 50%, 
        rgba(0,0,0,0) 0%, 
        rgba(30,20,60,0.3) 40%, 
        rgba(0,0,0,0.8) 100%);
    opacity: 0.6;
    pointer-events: none;
}

/* 动画定义 */
@keyframes spiralRotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes ringPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes particlePulse {
    0%, 100% {
        opacity: 0.2;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.5);
    }
}

@keyframes stardustFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100px) translateX(50px);
        opacity: 0;
    }
}

@keyframes meteorFly {
    0% {
        transform: rotate(-45deg) translateX(-100px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: rotate(-45deg) translateX(calc(100vw + 100px));
        opacity: 0;
    }
}

/* 主容器 */
.space-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
    z-index: 1;
}

.space-content {
    width: 100%;
    max-width: 1200px;
    z-index: 2;
}

/* 现代化卡片设计 */
.modern-card {
    background: rgba(17, 24, 39, 0.6);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(75, 85, 99, 0.6);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
}

.modern-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, transparent 50%, rgba(147, 51, 234, 0.05) 100%);
    pointer-events: none;
}

.modern-card .card-glow-top {
    position: absolute;
    top: -112px;
    right: -112px;
    width: 224px;
    height: 224px;
    background: radial-gradient(circle, rgba(6, 182, 212, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: cardGlowPulse 4s ease-in-out infinite;
}

.modern-card .card-glow-bottom {
    position: absolute;
    bottom: -112px;
    left: -112px;
    width: 224px;
    height: 224px;
    background: radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: cardGlowPulse 4s ease-in-out infinite;
    animation-delay: 1s;
}

@keyframes cardGlowPulse {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* 现代化按钮 */
.modern-btn {
    position: relative;
    padding: 12px 32px;
    border-radius: 9999px;
    font-weight: 600;
    font-size: 18px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    overflow: hidden;
    cursor: pointer;
    border: none;
}

.modern-btn-primary {
    background: linear-gradient(45deg, #3b82f6, #9333ea);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.modern-btn-primary:hover {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
    transform: translateY(-2px) scale(1.05);
}

.modern-btn-primary::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, #9333ea, #ec4899);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: left;
}

.modern-btn-primary:hover::before {
    transform: scaleX(1);
}

.modern-btn-secondary {
    background: transparent;
    color: #06b6d4;
    border: 2px solid #06b6d4;
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.2);
}

.modern-btn-secondary:hover {
    background: rgba(6, 182, 212, 0.1);
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
    transform: translateY(-2px) scale(1.05);
}

.modern-btn span {
    position: relative;
    z-index: 1;
}

/* 按钮容器 */
.space-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;
    align-items: center;
    margin-bottom: 64px;
    padding: 0 20px;
}

@media (min-width: 768px) {
    .space-buttons {
        flex-direction: row;
        gap: 32px;
        margin-bottom: 80px;
    }
}

/* 标题样式 */
.space-header {
    text-align: center;
    margin-bottom: 48px;
}

.space-title {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 800;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #06b6d4, #9333ea, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titlePulse 3s ease-in-out infinite;
    text-align: center;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.title-divider {
    height: 4px;
    width: 160px;
    margin: 0 auto;
    background: linear-gradient(90deg, transparent, #06b6d4, #9333ea, #ec4899, transparent);
    border-radius: 2px;
    animation: dividerPulse 3s ease-in-out infinite;
}

@keyframes dividerPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scaleX(1);
    }
    50% {
        opacity: 1;
        transform: scaleX(1.1);
    }
}

@keyframes titlePulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

.space-subtitle {
    font-size: clamp(1.25rem, 3vw, 2rem);
    color: #d1d5db;
    margin-bottom: 48px;
    text-align: center;
    line-height: 1.4;
}

.space-description {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: #06b6d4;
    text-align: center;
    margin-bottom: 48px;
    animation: descriptionPulse 4s ease-in-out infinite;
}

@keyframes descriptionPulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

/* 统计数据 */
.space-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 80px;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.stat-item {
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 30px 25px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 160px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(147, 51, 234, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-number {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 12px;
    animation: statPulse 3s ease-in-out infinite;
    position: relative;
    z-index: 1;
    line-height: 1.1;
    white-space: nowrap;
    text-align: center;
}

.stat-item:nth-child(1) .stat-number {
    background: linear-gradient(45deg, #06b6d4, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-item:nth-child(2) .stat-number {
    background: linear-gradient(45deg, #9333ea, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation-delay: 1s;
}

.stat-item:nth-child(3) .stat-number {
    background: linear-gradient(45deg, #ec4899, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation-delay: 2s;
}

.stat-label {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    color: #d1d5db;
    transition: color 0.3s ease;
    font-weight: 500;
    position: relative;
    z-index: 1;
    line-height: 1.2;
}

.stat-item:hover .stat-label {
    color: #ffffff;
}

@keyframes statPulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* 用户dashboard样式 */
.space-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    position: relative;
    z-index: 2;
}

.welcome-panel {
    text-align: center;
    margin-bottom: 60px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.space-welcome {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #06b6d4, #9333ea, #ec4899);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    line-height: 1.2;
}

.space-welcome-sub {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    color: #d1d5db;
    opacity: 0.9;
    line-height: 1.4;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* 星际导航 */
.space-navigation {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.nav-planet {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    min-height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-planet:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.nav-planet-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(147, 51, 234, 0.1));
    opacity: 0;
    transition: opacity 0.4s ease;
}

.nav-planet:hover .nav-planet-glow {
    opacity: 1;
}

.nav-planet-content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.nav-planet-content i {
    font-size: 3rem;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #06b6d4, #9333ea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
}

.nav-planet-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
    line-height: 1.3;
}

.nav-planet-content p {
    font-size: 0.95rem;
    color: #d1d5db;
    opacity: 0.8;
    line-height: 1.4;
}

/* 热门讨论区域 */
.space-discussions {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 25px;
    text-align: center;
    background: linear-gradient(45deg, #06b6d4, #9333ea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.discussion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.discussion-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
}

.discussion-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.card-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(147, 51, 234, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.discussion-card:hover .card-glow {
    opacity: 1;
}

.card-content {
    position: relative;
    z-index: 1;
}

.card-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 10px;
    line-height: 1.3;
}

.card-content p {
    font-size: 0.9rem;
    color: #d1d5db;
    opacity: 0.8;
    line-height: 1.5;
    margin-bottom: 15px;
}

.card-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #a1a1aa;
}

.card-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* === 学校列表页面样式 === */
.space-container {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 40px;
    margin-bottom: 30px;
}

.space-header {
    text-align: center;
    margin-bottom: 40px;
}

.space-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #06b6d4, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 12px;
    text-shadow: 0 2px 10px rgba(6, 182, 212, 0.3);
}

.space-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    margin: 0;
}

.space-search-panel {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 40px;
}

.search-bar-container {
    margin-bottom: 30px;
}

.space-search-box {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50px;
    overflow: hidden;
    border: 2px solid rgba(6, 182, 212, 0.3);
    transition: all 0.3s ease;
}

.space-search-box:focus-within {
    border-color: rgba(6, 182, 212, 0.6);
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
}

.space-input {
    flex: 1;
    padding: 16px 24px;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    outline: none;
}

.space-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.space-search-btn {
    background: linear-gradient(45deg, #06b6d4, #8b5cf6);
    border: none;
    padding: 16px 24px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.space-search-btn:hover {
    background: linear-gradient(45deg, #0891b2, #7c3aed);
    transform: scale(1.05);
}

.space-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.space-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.space-select {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
}

.space-select:focus {
    border-color: rgba(6, 182, 212, 0.5);
    box-shadow: 0 0 12px rgba(6, 182, 212, 0.2);
}

.space-select option {
    background: #1a1a2e;
    color: white;
}

.space-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.space-reset-btn {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.space-reset-btn:hover {
    color: #ec4899;
    text-decoration: none;
}

.space-stats {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.space-stats .highlight {
    color: #06b6d4;
    font-weight: 700;
    font-size: 16px;
}

.space-schools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
}

.space-school-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.space-school-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(6, 182, 212, 0.1), rgba(139, 92, 246, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.space-school-card:hover::before {
    opacity: 1;
}

.space-school-card:hover {
    transform: translateY(-8px);
    border-color: rgba(6, 182, 212, 0.4);
    box-shadow: 0 20px 40px rgba(6, 182, 212, 0.2);
}

.school-icon {
    text-align: center;
    margin-bottom: 16px;
    position: relative;
    z-index: 2;
}

.school-icon i {
    font-size: 48px;
    background: linear-gradient(45deg, #06b6d4, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.school-info {
    position: relative;
    z-index: 2;
}

.school-name {
    color: white;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 12px;
    text-align: center;
    line-height: 1.4;
}

.school-location {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    text-align: center;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.school-tags {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.space-tag {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.space-tag.type {
    background: rgba(6, 182, 212, 0.2);
    color: #06b6d4;
    border: 1px solid rgba(6, 182, 212, 0.3);
}

.space-tag.level {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.space-empty-state {
    text-align: center;
    padding: 80px 20px;
    color: rgba(255, 255, 255, 0.8);
}

.empty-icon {
    margin-bottom: 24px;
}

.empty-icon i {
    font-size: 80px;
    background: linear-gradient(45deg, #06b6d4, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0.6;
}

.space-empty-state h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: white;
}

.space-empty-state p {
    font-size: 1.1rem;
    margin-bottom: 32px;
    color: rgba(255, 255, 255, 0.7);
}

.space-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 28px;
    background: linear-gradient(45deg, #06b6d4, #8b5cf6);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.space-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(6, 182, 212, 0.3);
    text-decoration: none;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-spiral {
        width: 600px;
        height: 600px;
    }

    .secondary-spiral {
        width: 900px;
        height: 900px;
    }

    .space-container {
        padding: 24px;
        margin: 16px;
    }
    
    .space-header h1 {
        font-size: 2rem;
    }
    
    .space-filters {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .space-schools-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .space-actions {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .space-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-top: 50px;
        padding: 0 10px;
        max-width: 100%;
    }

    .stat-item {
        padding: 20px 15px;
        margin: 0;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .stat-number {
        font-size: clamp(2.2rem, 8vw, 3.5rem);
        margin-bottom: 10px;
    }

    .stat-label {
        font-size: clamp(0.95rem, 3vw, 1.1rem);
    }

    .modern-card {
        padding: 24px;
    }

    .space-title {
        font-size: clamp(2.5rem, 10vw, 4.5rem);
        margin-bottom: 20px;
    }

    .space-subtitle {
        font-size: clamp(1.1rem, 4vw, 1.6rem);
        margin-bottom: 32px;
    }

    .space-description {
        font-size: clamp(0.95rem, 3vw, 1.1rem);
        margin-bottom: 40px;
    }

    /* Dashboard响应式 */
    .space-dashboard {
        padding: 20px 15px;
    }

    .welcome-panel {
        padding: 30px 20px;
        margin-bottom: 40px;
    }

    .space-navigation {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 40px;
    }

    .nav-planet {
        padding: 25px 20px;
        min-height: 140px;
    }

    .nav-planet-content i {
        font-size: 2.5rem;
        margin-bottom: 12px;
    }

    .nav-planet-content h3 {
        font-size: 1.2rem;
    }

    .space-discussions {
        padding: 25px 20px;
    }

    .discussion-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .discussion-card {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .main-spiral {
        width: 400px;
        height: 400px;
    }

    .secondary-spiral {
        width: 600px;
        height: 600px;
    }

    .space-stats {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 40px;
        padding: 0 20px;
        max-width: 320px;
    }

    .stat-item {
        padding: 25px 20px;
        max-width: 100%;
        margin: 0 auto;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .stat-number {
        font-size: clamp(2.2rem, 8vw, 3.2rem);
        margin-bottom: 8px;
    }

    .stat-label {
        font-size: clamp(0.95rem, 3.5vw, 1.1rem);
    }

    .modern-btn {
        padding: 12px 28px;
        font-size: 16px;
        width: 100%;
        max-width: 200px;
    }

    .space-container {
        padding: 15px;
    }

    .space-title {
        font-size: clamp(2rem, 12vw, 3.5rem);
        margin-bottom: 16px;
    }

    .space-subtitle {
        font-size: clamp(1rem, 5vw, 1.4rem);
        margin-bottom: 24px;
    }

    .space-description {
        font-size: clamp(0.9rem, 4vw, 1rem);
        margin-bottom: 32px;
    }

    /* 超小屏dashboard */
    .space-dashboard {
        padding: 15px 10px;
    }

    .welcome-panel {
        padding: 25px 15px;
        margin-bottom: 30px;
    }

    .space-navigation {
        gap: 15px;
        margin-bottom: 30px;
    }

    .nav-planet {
        padding: 20px 15px;
        min-height: 120px;
    }

    .nav-planet-content i {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .nav-planet-content h3 {
        font-size: 1.1rem;
    }

    .nav-planet-content p {
        font-size: 0.85rem;
    }

    .space-discussions {
        padding: 20px 15px;
    }

    .discussion-card {
        padding: 12px;
    }

    .card-content h4 {
        font-size: 1rem;
    }

    .card-content p {
        font-size: 0.85rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
    .space-stats {
        gap: 16px;
        margin-top: 35px;
        padding: 0 15px;
        max-width: 280px;
    }

    .stat-item {
        padding: 16px 12px;
    }

    .stat-number {
        font-size: clamp(1.8rem, 9vw, 2.8rem);
        margin-bottom: 6px;
    }

    .stat-label {
        font-size: clamp(0.85rem, 4vw, 1rem);
    }

    .space-title {
        font-size: clamp(1.8rem, 14vw, 3rem);
        margin-bottom: 12px;
    }

    .title-divider {
        width: 120px;
        height: 3px;
    }

    .space-subtitle {
        font-size: clamp(0.95rem, 5.5vw, 1.3rem);
        margin-bottom: 20px;
    }

    .space-description {
        font-size: clamp(0.85rem, 4.5vw, 0.95rem);
        margin-bottom: 28px;
    }

    .modern-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    /* 导航栏响应式 */
    .nav-container {
        padding: 0 15px;
        height: 60px;
    }

    .nav-brand a {
        font-size: 18px;
    }

    .nav-brand i {
        font-size: 16px;
    }

    .nav-menu {
        gap: 12px;
    }

    .nav-user, .nav-auth {
        gap: 10px;
    }

    .nav-link {
        padding: 8px 12px;
        font-size: 13px;
    }

    .welcome-text {
        font-size: 12px;
    }

    .flash-messages {
        right: 15px;
        left: 15px;
        max-width: none;
    }
}

/* 动画效果类 */
.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hidden {
    display: none !important;
}

/* 工具类 */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.min-h-screen {
    min-height: 100vh;
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.w-full {
    width: 100%;
}

.max-w-lg {
    max-width: 32rem;
}

.mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.group {
    position: relative;
}

.block {
    display: block;
}

.text-sm {
    font-size: 0.875rem;
}

.font-medium {
    font-weight: 500;
}

.text-gray-300 {
    color: #d1d5db;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-8 {
    margin-top: 2rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.mr-1 {
    margin-right: 0.25rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.text-center {
    text-align: center;
}

.text-3xl {
    font-size: 1.875rem;
}

.font-bold {
    font-weight: 700;
}

.text-transparent {
    color: transparent;
}

.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-cyan-400 {
    --tw-gradient-from: #22d3ee;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(34, 211, 238, 0));
}

.to-purple-500 {
    --tw-gradient-to: #a855f7;
}

.text-cyan-400 {
    color: #22d3ee;
}

.text-xs {
    font-size: 0.75rem;
}

.opacity-0 {
    opacity: 0;
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-500 {
    transition-duration: 500ms;
}

.grid {
    display: grid;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-3 {
    gap: 0.75rem;
}

.appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.inline-flex {
    display: inline-flex;
}

.hover\:text-cyan-300:hover {
    color: #67e8f9;
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.cursor-pointer {
    cursor: pointer;
}

.hover\:underline:hover {
    text-decoration-line: underline;
}

.group:hover .group-hover\:-translate-x-1 {
    transform: translateX(-0.25rem);
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}