/* 校园社交主题样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #333;
    padding: 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 70px;
}

.nav-brand a {
    color: #333;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-brand a:hover {
    opacity: 0.8;
}

.nav-brand i {
    color: #667eea !important;
    background: rgba(102, 126, 234, 0.1);
    padding: 8px;
    border-radius: 8px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-user, .nav-auth {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-text {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.nav-link {
    color: #666;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    border: 1px solid transparent;
}

.nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border-color: rgba(102, 126, 234, 0.2);
}

.register-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white !important;
    border: none;
}

.register-btn:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 消息提示样式 */
.flash-messages {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.flash-message {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
    backdrop-filter: blur(10px);
}

.flash-success {
    background: rgba(76, 175, 80, 0.9);
    color: white;
    border-left: 4px solid #4CAF50;
}

.flash-error {
    background: rgba(244, 67, 54, 0.9);
    color: white;
    border-left: 4px solid #f44336;
}

.flash-close {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    font-size: 16px;
    padding: 0;
    margin-left: 10px;
    opacity: 0.8;
}

.flash-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - 140px);
}

/* 页脚样式 */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #666;
    text-align: center;
    padding: 30px 0;
    margin-top: 50px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 主容器 */
.campus-container {
    min-height: 100vh;
    padding: 20px 0;
}

/* 欢迎页面样式 */
.welcome-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.welcome-hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    margin-bottom: 80px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 60px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hero-content {
    color: #333;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 15px;
}

.hero-title i {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: 50%;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #666;
    margin-bottom: 15px;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: #888;
    margin-bottom: 40px;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-3px);
}

/* 英雄图片区域 */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.campus-illustration {
    position: relative;
    text-align: center;
}

.campus-illustration > i {
    font-size: 12rem;
    color: #667eea;
    opacity: 0.8;
}

.floating-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.floating-icons i {
    font-size: 2rem;
    color: #764ba2;
    background: white;
    padding: 15px;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    animation: float 3s ease-in-out infinite;
}

.floating-icons i:nth-child(1) {
    animation-delay: 0s;
}

.floating-icons i:nth-child(2) {
    animation-delay: 1s;
}

.floating-icons i:nth-child(3) {
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* 特色功能区域 */
.features-section {
    margin-bottom: 80px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
    background: rgba(102, 126, 234, 0.1);
    padding: 20px;
    border-radius: 50%;
    display: inline-block;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* 统计数据区域 */
.stats-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    text-align: center;
}

.stat-item {
    color: #333;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
}

/* 已登录用户页面样式 */
.dashboard-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.welcome-banner {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.welcome-content h2 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 5px;
    font-weight: 600;
}

.welcome-content p {
    color: #666;
    font-size: 1.1rem;
}

.action-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

/* 导航网格 */
.nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.nav-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.nav-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 15px;
    border-radius: 12px;
    color: white;
    font-size: 1.5rem;
    min-width: 50px;
    text-align: center;
}

.nav-content h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 5px;
    font-weight: 600;
}

.nav-content p {
    color: #666;
    font-size: 0.9rem;
}

/* 内容区域 */
.content-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.section-header h3 {
    font-size: 1.5rem;
    color: #333;
    font-weight: 600;
}

.more-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.more-link:hover {
    color: #764ba2;
}

/* 帖子网格 */
.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.post-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.post-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: white;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.post-author {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #667eea;
    font-weight: 500;
}

.post-school {
    font-size: 0.85rem;
    color: #666;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 12px;
}

.post-content h4 {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
    line-height: 1.4;
}

.post-content p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.post-stats {
    display: flex;
    gap: 15px;
    color: #999;
    font-size: 0.85rem;
}

.post-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .welcome-hero {
        grid-template-columns: 1fr;
        gap: 40px;
        padding: 40px 30px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .welcome-banner {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .nav-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-card {
        padding: 20px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 30px;
    }
    
    .posts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .campus-container {
        padding: 10px 0;
    }
    
    .welcome-section,
    .dashboard-section {
        padding: 0 15px;
    }
    
    .welcome-hero {
        padding: 30px 20px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .btn-primary,
    .btn-secondary {
        padding: 12px 20px;
        font-size: 1rem;
    }
    
    .feature-card {
        padding: 30px 20px;
    }
    
    .stats-section {
        padding: 30px 20px;
    }
    
    .welcome-banner {
        padding: 20px;
    }
    
    .nav-card {
        flex-direction: column;
        text-align: center;
    }
    
    .post-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}