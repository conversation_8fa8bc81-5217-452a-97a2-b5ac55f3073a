{% extends "base.html" %}

{% block title %}学校列表 - 星际论坛{% endblock %}

{% block content %}
<div class="space-dashboard">
    <div class="space-container">
        <div class="space-header">
            <h1><i class="fas fa-university"></i> 星际高校导航</h1>
            <p>探索全国高校，寻找你的学术星球</p>
        </div>
        
        <!-- 筛选和搜索区域 -->
        <div class="space-search-panel">
            <form method="GET" action="{{ url_for('schools_list') }}">
                <!-- 搜索框 -->
                <div class="search-bar-container">
                    <div class="space-search-box">
                        <input type="text" 
                               name="q" 
                               value="{{ keyword or '' }}" 
                               placeholder="搜索学校名称..." 
                               class="space-input">
                        <button type="submit" class="space-search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 筛选选项 -->
                <div class="space-filters">
                    <div class="filter-group">
                        <label class="space-label">
                            <i class="fas fa-map-marker-alt"></i> 省份
                        </label>
                        <select name="province" 
                                id="province-select"
                                class="space-select">
                            <option value="">全部省份</option>
                            {% for province in provinces %}
                            <option value="{{ province }}" 
                                    {% if selected_province == province %}selected{% endif %}>
                                {{ province }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="space-label">
                            <i class="fas fa-city"></i> 城市
                        </label>
                        <select name="city" 
                                id="city-select"
                                class="space-select">
                            <option value="">全部城市</option>
                            {% for city in cities %}
                            <option value="{{ city }}" 
                                    {% if selected_city == city %}selected{% endif %}>
                                {{ city }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="space-label">
                            <i class="fas fa-graduation-cap"></i> 类型
                        </label>
                        <select name="type" 
                                class="space-select">
                            <option value="">全部类型</option>
                            {% for school_type in school_types %}
                            <option value="{{ school_type }}" 
                                    {% if selected_type == school_type %}selected{% endif %}>
                                {{ school_type }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <!-- 操作按钮和统计 -->
                <div class="space-actions">
                    <a href="{{ url_for('schools_list') }}" class="space-reset-btn">
                        <i class="fas fa-times"></i> 清除筛选
                    </a>
                    <div class="space-stats">
                        <i class="fas fa-star"></i>
                        共找到 <span class="highlight">{{ schools|length }}</span> 所学校
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 学校列表 -->
        {% if schools %}
        <div class="space-schools-grid">
            {% for school in schools %}
            <div class="space-school-card" onclick="location.href='{{ url_for('school_board', school_id=school.id) }}'">
                <div class="school-icon">
                    <i class="fas fa-university"></i>
                </div>
                <div class="school-info">
                    <h3 class="school-name">{{ school.name }}</h3>
                    <p class="school-location">
                        <i class="fas fa-map-marker-alt"></i>
                        {{ school.province }} {{ school.city }}
                    </p>
                    <div class="school-tags">
                        <span class="space-tag type">{{ school.school_type }}</span>
                        {% if school.level != school.school_type %}
                        <span class="space-tag level">{{ school.level }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="school-hover-effect"></div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="space-empty-state">
            <div class="empty-icon">
                <i class="fas fa-satellite-dish"></i>
            </div>
            <h3>未发现目标星球</h3>
            <p>请调整搜索参数，继续探索宇宙中的学术星球</p>
            <a href="{{ url_for('schools_list') }}" class="space-btn">
                <i class="fas fa-rocket"></i> 重新探索
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// 省份变化时更新城市列表
document.getElementById('province-select').addEventListener('change', function() {
    const province = this.value;
    const citySelect = document.getElementById('city-select');
    
    // 清空城市选项
    citySelect.innerHTML = '<option value="">全部城市</option>';
    
    if (province) {
        // 获取该省份的城市列表
        fetch(`/api/cities/${encodeURIComponent(province)}`)
            .then(response => response.json())
            .then(data => {
                data.cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    citySelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('获取城市列表失败:', error);
            });
    }
});
</script>
{% endblock %}