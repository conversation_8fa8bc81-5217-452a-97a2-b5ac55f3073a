{% extends "base.html" %}

{% block title %}注册 - 高校论坛{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-background">
        <!-- 动态背景元素 -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
    </div>
    
    <div class="auth-content register-content">
        <div class="auth-card register-card">
            <!-- 返回首页按钮 -->
            <a href="{{ url_for('index') }}" class="back-link">
                <i class="fas fa-arrow-left"></i>
                返回首页
            </a>
            
            <!-- 注册头部 -->
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h1 class="auth-title">加入我们</h1>
                <p class="auth-subtitle">创建您的账户开始交流</p>
            </div>
            
            <!-- 注册表单 -->
            <form method="POST" action="{{ url_for('register') }}" class="auth-form register-form" id="registerForm">
                <div class="form-row">
                    <div class="input-group">
                        <div class="input-wrapper">
                            <i class="input-icon fas fa-user"></i>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                required
                                maxlength="50"
                                autocomplete="username"
                            />
                            <label for="username">用户名</label>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <div class="input-wrapper">
                            <i class="input-icon fas fa-envelope"></i>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                required
                                autocomplete="email"
                            />
                            <label for="email">邮箱地址</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="input-group">
                        <div class="input-wrapper">
                            <i class="input-icon fas fa-lock"></i>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                minlength="6"
                                autocomplete="new-password"
                            />
                            <label for="password">密码</label>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength">
                            <div class="strength-bar"></div>
                            <div class="strength-text">密码强度</div>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <div class="input-wrapper">
                            <i class="input-icon fas fa-lock"></i>
                            <input
                                type="password"
                                id="confirm_password"
                                name="confirm_password"
                                required
                                minlength="6"
                                autocomplete="new-password"
                            />
                            <label for="confirm_password">确认密码</label>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-match">
                            <i class="fas fa-check"></i>
                            <span>密码一致</span>
                        </div>
                    </div>
                </div>
                <!-- 学校选择区域 -->
                <div class="input-group">
                    <div class="school-selection">
                        <label class="selection-title">
                            <i class="fas fa-university"></i>
                            选择您的学校
                        </label>

                        <!-- 学校选择 -->
                        <div class="school-select-wrapper">
                            <select id="school_id" name="school_id" required class="school-select">
                                <option value="">请选择您的学校</option>
                                {% for school in schools %}
                                <option value="{{ school.id }}"
                                        data-province="{{ school.province or '' }}"
                                        data-city="{{ school.city or '' }}"
                                        data-type="{{ school.school_type or '' }}">
                                    {{ school.name }}
                                    {% if school.province and school.city %} - {{ school.province }} {{ school.city }}{% endif %}
                                    {% if school.school_type %} ({{ school.school_type }}){% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <i class="fas fa-chevron-down select-arrow"></i>
                        </div>
                        
                        <!-- 选中学校信息 -->
                        <div id="school-info" class="school-info-card">
                            <div class="info-content">
                                <h4 id="selected-school-name"></h4>
                                <p id="selected-school-location"></p>
                                <span id="selected-school-type"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 注册按钮 -->
                <button type="submit" class="auth-btn auth-btn-primary register-btn">
                    <span class="btn-text">创建账户</span>
                    <div class="btn-loading">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>

            <!-- 分割线 -->
            <div class="auth-divider">
                <span>或</span>
            </div>

            <!-- 登录链接 -->
            <div class="auth-footer">
                <p>已有账户？</p>
                <a href="{{ url_for('login') }}" class="auth-link">立即登录</a>
            </div>
        </div>
    </div>
</div>

<!-- 粒子效果 -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<script>
// 密码显示/隐藏切换
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentElement.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// 密码强度检测
function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    
    return strength;
}

// 更新密码强度显示
function updatePasswordStrength(password) {
    const strengthBar = document.querySelector('.strength-bar');
    const strengthText = document.querySelector('.strength-text');
    const strength = checkPasswordStrength(password);
    
    strengthBar.classList.remove('weak', 'medium', 'strong');
    
    if (password.length === 0) {
        strengthBar.style.opacity = '0';
        strengthText.textContent = '密码强度';
    } else if (strength <= 2) {
        strengthBar.classList.add('weak');
        strengthBar.style.opacity = '1';
        strengthText.textContent = '密码强度：弱';
        strengthText.style.color = '#ef4444';
    } else if (strength <= 3) {
        strengthBar.classList.add('medium');
        strengthBar.style.opacity = '1';
        strengthText.textContent = '密码强度：中等';
        strengthText.style.color = '#f59e0b';
    } else {
        strengthBar.classList.add('strong');
        strengthBar.style.opacity = '1';
        strengthText.textContent = '密码强度：强';
        strengthText.style.color = '#10b981';
    }
}

// 删除IP定位相关功能

// 表单验证和提交处理
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('.auth-btn-primary');
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const schoolId = document.getElementById('school_id').value;
    
    // 验证密码匹配
    if (password !== confirmPassword) {
        e.preventDefault();
        showError('两次输入的密码不一致');
        return false;
    }
    
    // 验证学校选择
    if (!schoolId) {
        e.preventDefault();
        showError('请选择您的学校');
        return false;
    }
    
    // 显示加载状态
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
});

// 错误提示函数
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    
    const existingError = document.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    const form = document.querySelector('.auth-form');
    form.insertBefore(errorDiv, form.firstChild);
    
    setTimeout(() => {
        errorDiv.remove();
    }, 5000);
}

// 输入框焦点效果和自动填充检测
function initInputs() {
    document.querySelectorAll('.input-wrapper input').forEach(input => {
        // 焦点事件
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // 输入事件
        input.addEventListener('input', function() {
            if (this.value) {
                this.parentElement.classList.add('focused');
            } else {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // 检查初始值和自动填充
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
        
        // 检测自动填充（延迟检查）
        setTimeout(() => {
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        }, 100);
    });
}

// 初始化输入框
initInputs();

// 密码输入处理
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const strengthContainer = document.querySelector('.password-strength');
    
    // 显示密码强度
    if (password.length > 0) {
        strengthContainer.classList.add('show');
        updatePasswordStrength(password);
    } else {
        strengthContainer.classList.remove('show');
    }
    
    // 检查密码匹配
    checkPasswordMatch();
});

// 确认密码输入处理
document.getElementById('confirm_password').addEventListener('input', function() {
    checkPasswordMatch();
});

// 检查密码匹配
function checkPasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const matchIndicator = document.querySelector('.password-match');
    
    if (confirmPassword.length > 0) {
        matchIndicator.classList.add('show');
        
        if (password === confirmPassword) {
            matchIndicator.classList.remove('error');
            matchIndicator.innerHTML = '<i class="fas fa-check"></i><span>密码一致</span>';
        } else {
            matchIndicator.classList.add('error');
            matchIndicator.innerHTML = '<i class="fas fa-times"></i><span>密码不一致</span>';
        }
    } else {
        matchIndicator.classList.remove('show');
    }
}

// 移除学校搜索功能

// 学校选择处理
document.getElementById('school_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const schoolInfoCard = document.getElementById('school-info');
    
    if (selectedOption.value) {
        const schoolName = selectedOption.textContent.split(' - ')[0].split(' (')[0];
        const province = selectedOption.getAttribute('data-province');
        const city = selectedOption.getAttribute('data-city');
        const type = selectedOption.getAttribute('data-type');
        
        document.getElementById('selected-school-name').textContent = schoolName;
        document.getElementById('selected-school-location').textContent = 
            province && city ? `📍 ${province} ${city}` : '';
        document.getElementById('selected-school-type').textContent = 
            type ? `🏫 ${type}` : '';
        
        schoolInfoCard.classList.add('show');
    } else {
        schoolInfoCard.classList.remove('show');
    }
});



// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面加载动画
    document.querySelector('.auth-card').classList.add('fade-in');
});
</script>
{% endblock %}