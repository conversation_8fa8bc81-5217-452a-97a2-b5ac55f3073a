{% extends "base.html" %}

{% block title %}登录 - 高校论坛{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-background">
        <!-- 动态背景元素 -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
    </div>
    
    <div class="auth-content">
        <div class="auth-card">
            <!-- 返回首页按钮 -->
            <a href="{{ url_for('index') }}" class="back-link">
                <i class="fas fa-arrow-left"></i>
                返回首页
            </a>
            
            <!-- 登录头部 -->
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1 class="auth-title">欢迎回来</h1>
                <p class="auth-subtitle">登录您的账户继续探索</p>
            </div>
            
            <!-- 登录表单 -->
            <form method="POST" class="auth-form" id="loginForm">
                <div class="input-group">
                    <div class="input-wrapper">
                        <i class="input-icon fas fa-user"></i>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            required
                            autocomplete="username"
                        />
                        <label for="username">用户名或邮箱</label>
                    </div>
                </div>

                <div class="input-group">
                    <div class="input-wrapper">
                        <i class="input-icon fas fa-lock"></i>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            required
                            autocomplete="current-password"
                        />
                        <label for="password">密码</label>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="remember" name="remember">
                        <span class="checkmark"></span>
                        记住我
                    </label>
                </div>

                <button type="submit" class="auth-btn auth-btn-primary">
                    <span class="btn-text">登录</span>
                    <div class="btn-loading">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>

            <!-- 分割线 -->
            <div class="auth-divider">
                <span>或</span>
            </div>

            <!-- 注册链接 -->
            <div class="auth-footer">
                <p>还没有账户？</p>
                <a href="{{ url_for('register') }}" class="auth-link">立即注册</a>
            </div>
        </div>
    </div>
</div>

<!-- 粒子效果 -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<script>
// 密码显示/隐藏切换
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentElement.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// 表单提交处理
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('.auth-btn-primary');
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        e.preventDefault();
        showError('请填写完整的登录信息');
        return false;
    }
    
    // 显示加载状态
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
});

// 错误提示函数
function showError(message) {
    // 创建错误提示
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    
    // 移除现有错误提示
    const existingError = document.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // 添加到表单前
    const form = document.querySelector('.auth-form');
    form.insertBefore(errorDiv, form.firstChild);
    
    // 3秒后自动移除
    setTimeout(() => {
        errorDiv.remove();
    }, 3000);
}

// 输入框焦点效果和自动填充检测
function initInputs() {
    document.querySelectorAll('.input-wrapper input').forEach(input => {
        // 焦点事件
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // 输入事件
        input.addEventListener('input', function() {
            if (this.value) {
                this.parentElement.classList.add('focused');
            } else {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // 检查初始值和自动填充
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
        
        // 检测自动填充（延迟检查）
        setTimeout(() => {
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        }, 100);
    });
}

// 初始化输入框
initInputs();
</script>
{% endblock %}